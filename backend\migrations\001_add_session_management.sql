-- Migration: Add Session Management for Readdy-Style Intent Generation
-- Version: 001
-- Created: 2024-01-XX
-- Description: Adds tables for session-based context management and element interaction tracking

-- =====================================================
-- 1. PROTOTYPE SESSIONS TABLE
-- =====================================================
-- Stores page sessions with context but without full HTML in main queries
CREATE TABLE prototype_sessions (
    id VARCHAR(36) PRIMARY KEY,
    prototype_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    page_url VARCHAR(500) NOT NULL,
    page_html LONGTEXT NOT NULL,
    session_state ENUM('active', 'editing', 'completed', 'expired') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (DATE_ADD(NOW(), INTERVAL 24 HOUR)),
    
    -- Foreign key constraints
    FOREIGN KEY (prototype_id) REFERENCES prototypes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes for performance
    INDEX idx_user_prototype (user_id, prototype_id),
    INDEX idx_session_state (session_state),
    INDEX idx_last_accessed (last_accessed),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 2. ELEMENT INTERACTIONS TABLE
-- =====================================================
-- Tracks specific element interactions for context building
CREATE TABLE element_interactions (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    element_selector VARCHAR(500) NOT NULL,
    element_code TEXT NOT NULL,
    element_type VARCHAR(50) DEFAULT 'unknown',
    interaction_type ENUM('click', 'hover', 'focus', 'select') DEFAULT 'click',
    user_intent TEXT NULL,
    ai_suggestion TEXT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (session_id) REFERENCES prototype_sessions(id) ON DELETE CASCADE,
    
    -- Indexes for performance
    INDEX idx_session_interactions (session_id, created_at),
    INDEX idx_element_type (element_type),
    INDEX idx_interaction_type (interaction_type),
    INDEX idx_confidence_score (confidence_score)
);

-- =====================================================
-- 3. INTENT GENERATIONS TABLE
-- =====================================================
-- Stores intent generation results for caching and analytics
CREATE TABLE intent_generations (
    id VARCHAR(36) PRIMARY KEY,
    interaction_id VARCHAR(36) NOT NULL,
    element_hash VARCHAR(64) NOT NULL, -- MD5 hash of element_code for caching
    user_intent TEXT NOT NULL,
    ai_suggestion TEXT NOT NULL,
    confidence_level ENUM('high', 'medium', 'low') DEFAULT 'medium',
    token_usage INT DEFAULT 0,
    response_time_ms INT DEFAULT 0,
    llm_provider VARCHAR(50) DEFAULT 'litellm',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (interaction_id) REFERENCES element_interactions(id) ON DELETE CASCADE,
    
    -- Indexes for performance and caching
    INDEX idx_element_hash (element_hash),
    INDEX idx_confidence_level (confidence_level),
    INDEX idx_token_usage (token_usage),
    INDEX idx_response_time (response_time_ms),
    INDEX idx_llm_provider (llm_provider),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 4. SESSION ANALYTICS TABLE
-- =====================================================
-- Tracks session-level metrics for performance monitoring
CREATE TABLE session_analytics (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    total_interactions INT DEFAULT 0,
    successful_intents INT DEFAULT 0,
    failed_intents INT DEFAULT 0,
    total_tokens_used INT DEFAULT 0,
    avg_response_time_ms DECIMAL(8,2) DEFAULT 0.00,
    session_duration_seconds INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (session_id) REFERENCES prototype_sessions(id) ON DELETE CASCADE,
    
    -- Indexes for analytics
    INDEX idx_session_metrics (session_id),
    INDEX idx_performance_metrics (avg_response_time_ms, total_tokens_used),
    INDEX idx_success_rate (successful_intents, failed_intents)
);

-- =====================================================
-- 5. TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Trigger to update session analytics on new interactions
DELIMITER $$
CREATE TRIGGER update_session_analytics_on_interaction
    AFTER INSERT ON element_interactions
    FOR EACH ROW
BEGIN
    INSERT INTO session_analytics (id, session_id, total_interactions)
    VALUES (UUID(), NEW.session_id, 1)
    ON DUPLICATE KEY UPDATE
        total_interactions = total_interactions + 1,
        updated_at = CURRENT_TIMESTAMP;
END$$

-- Trigger to update session last_accessed on interaction
CREATE TRIGGER update_session_last_accessed
    AFTER INSERT ON element_interactions
    FOR EACH ROW
BEGIN
    UPDATE prototype_sessions 
    SET last_accessed = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.session_id;
END$$

DELIMITER ;

-- =====================================================
-- 6. CLEANUP PROCEDURES
-- =====================================================

-- Procedure to clean up expired sessions
DELIMITER $$
CREATE PROCEDURE CleanupExpiredSessions()
BEGIN
    DECLARE session_count INT DEFAULT 0;
    
    -- Count sessions to be deleted
    SELECT COUNT(*) INTO session_count
    FROM prototype_sessions 
    WHERE expires_at < NOW() OR last_accessed < DATE_SUB(NOW(), INTERVAL 48 HOUR);
    
    -- Delete expired sessions (cascades to related tables)
    DELETE FROM prototype_sessions 
    WHERE expires_at < NOW() OR last_accessed < DATE_SUB(NOW(), INTERVAL 48 HOUR);
    
    -- Log cleanup activity
    INSERT INTO system_logs (id, log_level, message, created_at)
    VALUES (UUID(), 'INFO', CONCAT('Cleaned up ', session_count, ' expired sessions'), NOW());
END$$

DELIMITER ;

-- =====================================================
-- 7. INITIAL DATA AND CONFIGURATION
-- =====================================================

-- Create indexes for existing prototypes table if needed
-- (Assuming prototypes table exists from previous schema)
-- ALTER TABLE prototypes ADD INDEX idx_user_created (user_id, created_at) IF NOT EXISTS;

-- Set up automatic cleanup event (runs every 6 hours)
SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS cleanup_expired_sessions
ON SCHEDULE EVERY 6 HOUR
STARTS CURRENT_TIMESTAMP
DO CALL CleanupExpiredSessions();

-- =====================================================
-- 8. PERFORMANCE OPTIMIZATION
-- =====================================================

-- Optimize for common query patterns
-- Most frequent: Get active session by user + prototype
-- CREATE INDEX idx_active_user_sessions ON prototype_sessions (user_id, prototype_id, session_state, last_accessed);

-- Most frequent: Get recent interactions for session
-- CREATE INDEX idx_recent_interactions ON element_interactions (session_id, created_at DESC);

-- Cache optimization: Find similar elements
-- CREATE INDEX idx_element_cache ON intent_generations (element_hash, confidence_level, created_at DESC);
